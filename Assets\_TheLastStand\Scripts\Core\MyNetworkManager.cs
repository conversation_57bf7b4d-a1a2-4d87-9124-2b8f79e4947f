using Mirror;
using Steamworks;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MyNetworkManager : NetworkManager
{
    public static bool isMultiplayer;
    public static MyNetworkManager instance; // Static instance

    public override void Awake()
    {
        base.Awake();
        if (instance == null) 
        {
            instance = this;
        }
        else 
        {
            Destroy(gameObject); 
            return;
        }
        EnsureLobbyPlayerListExists();
    }

    public override void OnStartServer()
    {
        base.OnStartServer();
        EnsureLobbyPlayerListExists();
    }

    private void EnsureLobbyPlayerListExists()
    {
        if (LobbyPlayerList.instance == null)
        {
            LobbyPlayerList listComponent = GetComponent<LobbyPlayerList>();
            if (listComponent == null)
            {
                Debug.LogWarning("MyNetworkManager: LobbyPlayerList component not found on NetworkManager GameObject. Please add it in the Editor, and ensure a NetworkIdentity is present.");
            }
            
            if (LobbyPlayerList.instance == null && listComponent != null)
            {
                Debug.Log("MyNetworkManager: LobbyPlayerList.instance was set by finding component.");
            }
            else if (LobbyPlayerList.instance != null)
            {
                Debug.Log("MyNetworkManager: LobbyPlayerList.instance is available.");
            }
            else
            {
                Debug.LogError("MyNetworkManager: Failed to find LobbyPlayerList.instance. Critical error.");
            }
        }
    }

    public override void OnServerAddPlayer(NetworkConnectionToClient conn)
    {
        // Enhanced NavMesh-aware spawn position validation
        if (playerPrefab != null)
        {
            Vector3 spawnPosition = GetValidSpawnPosition();

            // Create player at validated spawn position
            GameObject player = Instantiate(playerPrefab, spawnPosition, Quaternion.identity);

            // Ensure NavMeshAgent is properly configured if present
            var navAgent = player.GetComponent<UnityEngine.AI.NavMeshAgent>();
            if (navAgent != null)
            {
                // Disable agent temporarily to prevent conflicts during spawn
                navAgent.enabled = false;

                // Re-enable after a frame to ensure proper initialization
                StartCoroutine(EnableNavAgentAfterFrame(navAgent, spawnPosition));
            }

            NetworkServer.AddPlayerForConnection(conn, player);
            Debug.Log($"MyNetworkManager: Successfully spawned player at validated position {spawnPosition}");
        }
        else
        {
            Debug.LogError("MyNetworkManager: Player prefab is null, cannot spawn player.");
        }

        // Validate client component exists before proceeding
        if (conn.identity != null)
        {
            MyClient client = conn.identity.GetComponent<MyClient>();
            if (client != null)
            {
                InitializePlayerClient(conn, client);

                if (LobbyPlayerList.instance != null)
                {
                    LobbyPlayerList.instance.allClients.Add(client);
                }
                else
                {
                    Debug.LogError("MyNetworkManager: LobbyPlayerList.instance is null. Cannot add client to list.");
                }
            }
            else
            {
                Debug.LogError("MyNetworkManager: MyClient component not found on spawned player object.", conn.identity.gameObject);
            }
        }
        else
        {
            Debug.LogError("MyNetworkManager: Connection identity is null during OnServerAddPlayer.");
        }
    }

    private Vector3 GetValidSpawnPosition()
    {
        Transform startPos = GetStartPosition();
        if (startPos == null)
        {
            Debug.LogWarning("MyNetworkManager: No start position found, using world origin.");
            return Vector3.zero;
        }

        Vector3 originalPosition = startPos.position;

        // Check if original position is on NavMesh
        UnityEngine.AI.NavMeshHit hit;
        if (UnityEngine.AI.NavMesh.SamplePosition(originalPosition, out hit, 2.0f, UnityEngine.AI.NavMesh.AllAreas))
        {
            Debug.Log($"MyNetworkManager: Original spawn position {originalPosition} is NavMesh valid.");
            return hit.position;
        }

        Debug.LogWarning($"MyNetworkManager: Original spawn position {originalPosition} is not on NavMesh. Searching for valid position.");

        // Try progressively larger search radii
        float[] searchRadii = { 5.0f, 10.0f, 20.0f, 50.0f };

        foreach (float radius in searchRadii)
        {
            if (UnityEngine.AI.NavMesh.SamplePosition(originalPosition, out hit, radius, UnityEngine.AI.NavMesh.AllAreas))
            {
                Debug.Log($"MyNetworkManager: Found valid NavMesh position at {hit.position} within radius {radius}.");
                return hit.position;
            }
        }

        // If no NavMesh position found, try to find any walkable surface
        if (Physics.Raycast(originalPosition + Vector3.up * 10f, Vector3.down, out RaycastHit groundHit, 20f))
        {
            Vector3 groundPosition = groundHit.point;
            if (UnityEngine.AI.NavMesh.SamplePosition(groundPosition, out hit, 5.0f, UnityEngine.AI.NavMesh.AllAreas))
            {
                Debug.Log($"MyNetworkManager: Found NavMesh position {hit.position} near ground surface.");
                return hit.position;
            }
        }

        Debug.LogError($"MyNetworkManager: Could not find any valid NavMesh position. Using original position {originalPosition} as fallback.");
        return originalPosition;
    }

    private System.Collections.IEnumerator EnableNavAgentAfterFrame(UnityEngine.AI.NavMeshAgent navAgent, Vector3 position)
    {
        yield return null; // Wait one frame

        if (navAgent != null && navAgent.gameObject != null)
        {
            // Warp to position to ensure proper NavMesh placement
            if (navAgent.isOnNavMesh)
            {
                navAgent.Warp(position);
            }

            navAgent.enabled = true;
            Debug.Log($"MyNetworkManager: NavMeshAgent enabled and warped to {position}");
        }
    }

    private void InitializePlayerClient(NetworkConnectionToClient conn, MyClient client)
    {
        if (client == null)
        {
            Debug.LogError("MyNetworkManager: Cannot initialize null client.");
            return;
        }

        if (conn == null)
        {
            Debug.LogError("MyNetworkManager: Cannot initialize client with null connection.");
            return;
        }

        try
        {
            CSteamID steamId = GetSteamIDForConnection(conn);
            SetPlayerInfoAndAvatar(client, steamId, conn);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"MyNetworkManager: Exception during client initialization: {ex.Message}");

            // Fallback initialization with basic data
            string fallbackName = (conn == NetworkServer.localConnection) ? "Host" : ("Player " + conn.connectionId.ToString());
            client.playerInfo = new PlayerInfoData(fallbackName, 0);
            client.avatarData = null;
        }
    }

    private CSteamID GetSteamIDForConnection(NetworkConnectionToClient conn)
    {
        if (conn == NetworkServer.localConnection)
        {
            return SteamUser.GetSteamID();
        }

        if (conn.authenticationData is CSteamID authSteamId && authSteamId.IsValid())
        {
            return authSteamId;
        }

        if (conn.authenticationData is ulong authUlongSteamIdValue && authUlongSteamIdValue != 0)
        {
            return new CSteamID(authUlongSteamIdValue);
        }
        
        // Try to get Steam ID from lobby members if available
        if (SteamLobby.LobbyID.IsValid() && SteamLobby.LobbyID.m_SteamID != 0)
        {
            int numLobbyMembers = SteamMatchmaking.GetNumLobbyMembers(SteamLobby.LobbyID);
            for (int i = 0; i < numLobbyMembers; i++)
            {
                CSteamID memberSteamId = SteamMatchmaking.GetLobbyMemberByIndex(SteamLobby.LobbyID, i);
                if (memberSteamId.IsValid() && memberSteamId != SteamUser.GetSteamID())
                {
                    // This is a basic approach - in a real implementation you'd want to match
                    // the connection to the specific Steam ID more precisely
                    return memberSteamId;
                }
            }
        }
        
        return CSteamID.Nil;
    }

    private void SetPlayerInfoAndAvatar(MyClient client, CSteamID steamId, NetworkConnectionToClient conn)
    {
        if (client == null)
        {
            Debug.LogError("MyNetworkManager: Cannot set player info on null client.");
            return;
        }

        if (conn == null)
        {
            Debug.LogError("MyNetworkManager: Cannot set player info with null connection.");
            return;
        }

        try
        {
            if (steamId.IsValid() && steamId != CSteamID.Nil)
            {
                string personaName = SteamFriends.GetFriendPersonaName(steamId);

                // Validate persona name before creating PlayerInfoData
                if (string.IsNullOrEmpty(personaName))
                {
                    personaName = "Steam User " + steamId.m_SteamID.ToString();
                    Debug.LogWarning($"MyNetworkManager: Empty persona name for Steam ID {steamId.m_SteamID}. Using fallback name: {personaName}");
                }

                // Create PlayerInfoData with validated data
                PlayerInfoData playerInfoData = new PlayerInfoData(personaName, steamId.m_SteamID);
                client.playerInfo = playerInfoData;

                Debug.Log($"MyNetworkManager: Set player info for Steam user: {personaName} (ID: {steamId.m_SteamID})");

                // Handle avatar data safely
                try
                {
                    Texture2D avatarTexture = SteamHelper.GetAvatar(steamId);
                    if (avatarTexture != null)
                    {
                        client.avatarData = avatarTexture.EncodeToPNG();
                        Destroy(avatarTexture);
                    }
                    else
                    {
                        client.avatarData = null;
                    }
                }
                catch (System.Exception avatarEx)
                {
                    Debug.LogWarning($"MyNetworkManager: Failed to get avatar for Steam ID {steamId.m_SteamID}: {avatarEx.Message}");
                    client.avatarData = null;
                }
            }
            else
            {
                string playerName = (conn == NetworkServer.localConnection) ? "Host" : ("Player " + conn.connectionId.ToString());
                PlayerInfoData playerInfoData = new PlayerInfoData(playerName, 0);
                client.playerInfo = playerInfoData;
                client.avatarData = null;

                Debug.Log($"MyNetworkManager: Set player info for non-Steam user: {playerName}");
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"MyNetworkManager: Exception in SetPlayerInfoAndAvatar: {ex.Message}");

            // Emergency fallback
            string fallbackName = (conn == NetworkServer.localConnection) ? "Host" : ("Player " + conn.connectionId.ToString());
            PlayerInfoData fallbackData = new PlayerInfoData(fallbackName, 0);
            client.playerInfo = fallbackData;
            client.avatarData = null;
        }
    }

    public override void OnServerDisconnect(NetworkConnectionToClient conn)
    {
        if (conn.identity != null)
        {
            MyClient client = conn.identity.GetComponent<MyClient>();
            if (client != null && LobbyPlayerList.instance != null)
            {
                LobbyPlayerList.instance.allClients.Remove(client);
            }
            else if (LobbyPlayerList.instance == null)
            {
                 Debug.LogError("LobbyPlayerList.instance is null during OnServerDisconnect. Cannot remove client from list.");
            }
        }
        base.OnServerDisconnect(conn);
    }

    public override void OnStartClient()
    {
        if (isMultiplayer)
        {
            MainMenu.instance.SetMenuState(MenuState.InParty);
            PopupManager.instance.Popup_Close();
        }

        base.OnStartClient();
    }

    public override void OnStopClient()
    {
        if (isMultiplayer)
        {
            if (LobbyPlayerList.instance != null && NetworkClient.active) // Check if client is active before clearing
            {
                Debug.Log("MyNetworkManager: OnStopClient - Clearing LobbyPlayerList.instance");
                LobbyPlayerList.instance.allClients.Clear();
            }
            MainMenu.instance.SetMenuState(MenuState.Home);
        }

        base.OnStopClient();
    }

    public override void OnStopServer()
    {
        if (LobbyPlayerList.instance != null)
        {
            LobbyPlayerList.instance.allClients.Clear();
        }
        base.OnStopServer();
    }

    public void SetMultiplayer(bool value)
    {
        isMultiplayer = value;

        if (isMultiplayer)
            NetworkServer.dontListen = false;
        else
            NetworkServer.dontListen = true;
    }
}